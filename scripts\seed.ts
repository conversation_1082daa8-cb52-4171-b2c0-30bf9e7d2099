import dotenv from 'dotenv'
import mongoose from 'mongoose'

// Load environment variables FIRST
dotenv.config({ path: '.env.local' })

// Now import other modules
import User from '../models/User'
import MenuItem from '../models/MenuItem'
import Table from '../models/Table'
import RestaurantSettings from '../models/Settings'

async function seedDatabase() {
  try {
    const MONGODB_URI = process.env.MONGODB_URI!
    if (!MONGODB_URI) {
      throw new Error('Please define the MONGODB_URI environment variable inside .env.local')
    }

    await mongoose.connect(MONGODB_URI)
    console.log('Connected to MongoDB')

    // Clear existing data
    await User.deleteMany({})
    await MenuItem.deleteMany({})
    await Table.deleteMany({})
    await RestaurantSettings.deleteMany({})
    console.log('Cleared existing data')

    // Create admin user
    const adminUser = new User({
      name: 'Admin User',
      email: '<EMAIL>',
      password: 'admin123',
      role: 'admin'
    })
    await adminUser.save()
    console.log('Created admin user')

    // Create staff user
    const staffUser = new User({
      name: 'Staff Member',
      email: '<EMAIL>',
      password: 'staff123',
      role: 'staff'
    })
    await staffUser.save()
    console.log('Created staff user')

    // Create tables
    const tables = []
    for (let i = 1; i <= 20; i++) {
      tables.push({
        number: i,
        capacity: i <= 10 ? 4 : i <= 15 ? 6 : 8,
        location: i <= 15 ? 'indoor' : 'outdoor'
      })
    }
    await Table.insertMany(tables)
    console.log('Created tables')

    // Create menu items
    const menuItems = [
      // Roti & Bread
      {
        name: 'Butter Roti',
        description: 'Soft wheat bread with butter',
        category: 'Roti & Bread',
        basePrice: 15,
        isVegetarian: true,
        preparationTime: 5
      },
      {
        name: 'Garlic Naan',
        description: 'Leavened bread with garlic and herbs',
        category: 'Roti & Bread',
        basePrice: 45,
        isVegetarian: true,
        preparationTime: 8
      },
      {
        name: 'Tandoori Roti',
        description: 'Whole wheat bread cooked in tandoor',
        category: 'Roti & Bread',
        basePrice: 20,
        isVegetarian: true,
        preparationTime: 6
      },

      // Rice & Biryani
      {
        name: 'Chicken Biryani',
        description: 'Aromatic basmati rice with spiced chicken',
        category: 'Rice & Biryani',
        basePrice: 180,
        variants: [
          { name: 'Half', price: 180, isAvailable: true },
          { name: 'Full', price: 320, isAvailable: true }
        ],
        isVegetarian: false,
        preparationTime: 25
      },
      {
        name: 'Veg Biryani',
        description: 'Aromatic basmati rice with mixed vegetables',
        category: 'Rice & Biryani',
        basePrice: 150,
        variants: [
          { name: 'Half', price: 150, isAvailable: true },
          { name: 'Full', price: 280, isAvailable: true }
        ],
        isVegetarian: true,
        preparationTime: 20
      },
      {
        name: 'Jeera Rice',
        description: 'Basmati rice with cumin seeds',
        category: 'Rice & Biryani',
        basePrice: 80,
        isVegetarian: true,
        preparationTime: 10
      },

      // Curries & Dal
      {
        name: 'Butter Chicken',
        description: 'Creamy tomato-based chicken curry',
        category: 'Curries & Dal',
        basePrice: 220,
        isVegetarian: false,
        preparationTime: 15
      },
      {
        name: 'Dal Tadka',
        description: 'Yellow lentils with tempering',
        category: 'Curries & Dal',
        basePrice: 120,
        isVegetarian: true,
        preparationTime: 12
      },
      {
        name: 'Paneer Butter Masala',
        description: 'Cottage cheese in rich tomato gravy',
        category: 'Curries & Dal',
        basePrice: 180,
        isVegetarian: true,
        preparationTime: 15
      },

      // Beverages
      {
        name: 'Masala Chai',
        description: 'Spiced Indian tea',
        category: 'Beverages',
        basePrice: 25,
        isVegetarian: true,
        preparationTime: 5
      },
      {
        name: 'Fresh Lime Water',
        description: 'Refreshing lime juice with water',
        category: 'Beverages',
        basePrice: 35,
        variants: [
          { name: 'Sweet', price: 35, isAvailable: true },
          { name: 'Salt', price: 35, isAvailable: true },
          { name: 'Masala', price: 40, isAvailable: true }
        ],
        isVegetarian: true,
        preparationTime: 3
      },
      {
        name: 'Lassi',
        description: 'Traditional yogurt drink',
        category: 'Beverages',
        basePrice: 50,
        variants: [
          { name: 'Sweet', price: 50, isAvailable: true },
          { name: 'Mango', price: 60, isAvailable: true },
          { name: 'Salted', price: 45, isAvailable: true }
        ],
        isVegetarian: true,
        preparationTime: 5
      }
    ]

    await MenuItem.insertMany(menuItems)
    console.log('Created menu items')

    // Create restaurant settings
    const settings = new RestaurantSettings({
      restaurantName: 'Dhabha Express',
      address: '123 Food Street, Spice City, India 400001',
      phone: '9876543210',
      email: '<EMAIL>',
      gstNumber: '27AAAAA0000A1Z5',
      currency: 'INR',
      taxRate: 18,
      serviceCharge: 10
    })
    await settings.save()
    console.log('Created restaurant settings')

    console.log('Database seeded successfully!')
    console.log('\nLogin credentials:')
    console.log('Admin: <EMAIL> / admin123')
    console.log('Staff: <EMAIL> / staff123')

  } catch (error) {
    console.error('Seeding error:', error)
  } finally {
    await mongoose.connection.close()
  }
}

// Run the seed function
seedDatabase()

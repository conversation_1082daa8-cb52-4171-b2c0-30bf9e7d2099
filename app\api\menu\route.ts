import { NextRequest, NextResponse } from 'next/server'
import connectDB from '@/lib/mongodb'
import MenuItem from '@/models/MenuItem'
import { requireAuth } from '@/lib/auth'

// GET /api/menu - Get all menu items
export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const available = searchParams.get('available')
    
    let query: any = {}
    
    if (category) {
      query.category = category
    }
    
    if (available === 'true') {
      query.isAvailable = true
    }
    
    const menuItems = await MenuItem.find(query).sort({ category: 1, name: 1 })
    
    return NextResponse.json({
      success: true,
      data: menuItems
    })
    
  } catch (error) {
    console.error('Get menu items error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch menu items' },
      { status: 500 }
    )
  }
}

// POST /api/menu - Create new menu item
export async function POST(request: NextRequest) {
  try {
    await requireAuth(request, 'manage_menu')
    await connectDB()
    
    const data = await request.json()
    
    const menuItem = new MenuItem(data)
    await menuItem.save()
    
    return NextResponse.json({
      success: true,
      message: 'Menu item created successfully',
      data: menuItem
    }, { status: 201 })
    
  } catch (error: any) {
    console.error('Create menu item error:', error)
    
    if (error.message === 'Authentication required' || error.message === 'Insufficient permissions') {
      return NextResponse.json(
        { error: error.message },
        { status: 401 }
      )
    }
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Failed to create menu item' },
      { status: 500 }
    )
  }
}

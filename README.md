# Dhabha POS - All-in-One Next.js App

This is an **all-in-one POS application** for dhabas, built as a **Next.js admin panel**. It provides an intuitive interface with a top navigation bar, offering easy access to all key features.

## Features

- **Login** – Secure login system for admin and staff users with modern UI design.
- **Dashboard** – Overview of orders, sales, and key metrics at a glance.
- **Order Management** – Manage all customer orders with table-wise tracking. Admin can:
  - Add new orders and assign to specific tables.
  - Update order status (e.g., Pending, Preparing, Ready, Served).
  - View order details and customer information.
  - Print bills directly from the order view.
- **Table Management** – Dynamic table management system with:
  - Real-time table status (Available, Occupied, Reserved, Cleaning).
  - Table capacity and location tracking.
  - Quick actions for table operations.
- **Menu Management** – Add, edit, and organize menu categories and items, including:
  - Item variants (e.g., Butter Roti, Plain Roti).
  - Vegetarian and spicy indicators.
  - Popular item tracking.
  - Category-wise organization.
- **Sales Reports** – Generate daily, weekly, and monthly sales reports for better business insights.
- **Settings** – Comprehensive settings for restaurant info, user profile, notifications, security, and printer setup.

## Technology Stack

- **Frontend & Backend:** Next.js 15 with App Router
- **Database:** MongoDB (ready for integration)
- **Styling:** Tailwind CSS with custom components
- **Icons:** Lucide React
- **Deployment:** Vercel (for hosting and build deployment)

## Getting Started

First, install dependencies:

```bash
npm install
```

Then, run the development server:

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

### Demo Credentials

- **Email:** <EMAIL>
- **Password:** admin123

## Project Structure

```
app/
├── (auth)/
│   ├── login/          # Login page with modern design
│   └── layout.tsx      # Auth layout
├── (dashboard)/
│   ├── orders/         # Order management
│   ├── tables/         # Table management
│   ├── menu/           # Menu management
│   ├── reports/        # Sales reports
│   ├── users/          # User management
│   ├── settings/       # Settings page
│   └── layout.tsx      # Dashboard layout
components/
├── layout/
│   ├── Header.tsx      # Top navigation
│   └── Sidebar.tsx     # Side navigation
└── ui/
    └── button.tsx      # Reusable button component
```

## POS Setup Recommendations

- **Tables:** Dynamic table creation with date-based order IDs (01-200925, 02-200925, etc.)
- **Menu Categories:** 5–15 categories (Roti, Curries, Beverages, Snacks, Desserts).
- **Menu Items:** 30–100 items depending on variety.
- **Staff Users:** 2–10 users can manage orders simultaneously.
- **Printers:** Support for receipt and kitchen printers.

## Features in Detail

### Order Management
- Dynamic order ID generation based on date
- Table-wise order tracking
- Order status management (Pending → Preparing → Ready → Served)
- Customer information tracking
- Order notes and special instructions

### Table Management
- Real-time table status updates
- Capacity-based table assignment
- Location-wise table organization
- Quick action buttons for common operations

### Menu Management
- Category-wise menu organization
- Item variants and pricing
- Vegetarian/Non-vegetarian indicators
- Spicy level indicators
- Popular item tracking

### Reports & Analytics
- Daily, weekly, and monthly sales reports
- Top-selling items analysis
- Revenue tracking and growth metrics
- Average order value calculations

This application is designed to be **fully deployable on Vercel**, allowing clients to manage their dhaba POS system efficiently, with **table-wise order management**, secure data storage capabilities, and comprehensive reporting.

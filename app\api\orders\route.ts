import { NextRequest, NextResponse } from 'next/server'
import connectDB from '@/lib/mongodb'
import Order from '@/models/Order'
import Table from '@/models/Table'
import { requireAuth } from '@/lib/auth'

// GET /api/orders - Get all orders
export async function GET(request: NextRequest) {
  try {
    await requireAuth(request, 'manage_orders')
    await connectDB()
    
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const table = searchParams.get('table')
    const limit = parseInt(searchParams.get('limit') || '50')
    const page = parseInt(searchParams.get('page') || '1')
    
    let query: any = {}
    
    if (status) {
      query.status = status
    }
    
    if (table) {
      query.table = table
    }
    
    const skip = (page - 1) * limit
    
    const orders = await Order.find(query)
      .populate('createdBy', 'name')
      .populate('servedBy', 'name')
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip(skip)

    // Post-process orders to handle table information
    const processedOrders = await Promise.all(orders.map(async (order) => {
      const orderObj = order.toObject()

      // Check if table is an ObjectId string and try to populate it
      const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(orderObj.table)
      if (isValidObjectId) {
        try {
          const table = await Table.findById(orderObj.table).select('number capacity')
          if (table) {
            orderObj.table = table
          } else {
            // ObjectId exists but table not found, treat as custom name
            orderObj.table = {
              _id: orderObj.table,
              number: orderObj.table,
              capacity: 4
            }
          }
        } catch (error) {
          // Error fetching table, treat as custom name
          orderObj.table = {
            _id: orderObj.table,
            number: orderObj.table,
            capacity: 4
          }
        }
      } else {
        // Custom table name, create virtual table object
        orderObj.table = {
          _id: orderObj.table,
          number: orderObj.table,
          capacity: 4
        }
      }

      return orderObj
    }))
    
    const total = await Order.countDocuments(query)
    
    return NextResponse.json({
      success: true,
      data: processedOrders,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
    
  } catch (error: any) {
    console.error('Get orders error:', error)
    
    if (error.message === 'Authentication required' || error.message === 'Insufficient permissions') {
      return NextResponse.json(
        { error: error.message },
        { status: 401 }
      )
    }
    
    return NextResponse.json(
      { error: 'Failed to fetch orders' },
      { status: 500 }
    )
  }
}

// POST /api/orders - Create new order
export async function POST(request: NextRequest) {
  try {
    const user = await requireAuth(request, 'manage_orders')
    await connectDB()
    
    const data = await request.json()

    // Validate table - support both ObjectId strings and custom table names
    if (!data.table || typeof data.table !== 'string' || data.table.trim().length === 0) {
      return NextResponse.json(
        { error: 'Table name is required' },
        { status: 400 }
      )
    }

    // If it looks like an ObjectId, validate it exists
    const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(data.table)
    if (isValidObjectId) {
      const table = await Table.findById(data.table)
      if (!table) {
        return NextResponse.json(
          { error: 'Table not found' },
          { status: 404 }
        )
      }
    }
    // For custom table names (like "Table-1"), we accept any non-empty string
    
    // Calculate total amount (handle empty items array)
    let totalAmount = 0
    if (data.items && Array.isArray(data.items)) {
      for (const item of data.items) {
        totalAmount += item.price * item.quantity
      }
    }

    // Calculate final amount, ensuring it's not negative
    const discount = data.discount || 0
    const finalAmount = Math.max(0, totalAmount - discount)

    const orderData = {
      ...data,
      items: data.items || [], // Ensure items is always an array
      totalAmount,
      finalAmount,
      createdBy: user._id
    }

    console.log('Creating order with data:', orderData)

    const order = new Order(orderData)

    // Ensure order number is generated before saving
    if (!order.orderNumber) {
      const today = new Date()
      const day = String(today.getDate()).padStart(2, '0')
      const month = String(today.getMonth() + 1).padStart(2, '0')
      const year = today.getFullYear()
      const dateStr = `${day}${month}${year}`

      const count = await Order.countDocuments({
        createdAt: {
          $gte: new Date(today.getFullYear(), today.getMonth(), today.getDate()),
          $lt: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1)
        }
      })

      order.orderNumber = `${count + 1}-${dateStr}`
      console.log('Generated order number:', order.orderNumber)
    }

    await order.save()
    console.log('Order saved successfully with number:', order.orderNumber)

    // Update table status only if it's a valid ObjectId (existing table)
    if (isValidObjectId) {
      await Table.findByIdAndUpdate(data.table, {
        status: 'occupied',
        currentOrder: order._id
      })
    }

    // Populate the order before returning
    if (isValidObjectId) {
      await order.populate('table', 'number capacity')
    }
    await order.populate('createdBy', 'name')
    
    return NextResponse.json({
      success: true,
      message: 'Order created successfully',
      data: order
    }, { status: 201 })
    
  } catch (error: any) {
    console.error('Create order error:', error)
    
    if (error.message === 'Authentication required' || error.message === 'Insufficient permissions') {
      return NextResponse.json(
        { error: error.message },
        { status: 401 }
      )
    }
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Failed to create order' },
      { status: 500 }
    )
  }
}

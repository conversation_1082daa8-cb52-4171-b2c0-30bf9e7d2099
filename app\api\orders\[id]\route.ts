import { NextRequest, NextResponse } from 'next/server'
import connectDB from '@/lib/mongodb'
import Order from '@/models/Order'
import Table from '@/models/Table'
import { requireAuth } from '@/lib/auth'

// GET /api/orders/[id] - Get single order
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await requireAuth(request, 'manage_orders')
    await connectDB()

    const { id } = await params
    const order = await Order.findById(id)
      .populate('createdBy', 'name')
      .populate('servedBy', 'name')
      .populate('items.menuItem', 'name price')

    if (!order) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }

    // Handle table information
    const orderObj = order.toObject()
    const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(orderObj.table)

    if (isValidObjectId) {
      try {
        const table = await Table.findById(orderObj.table).select('number capacity')
        if (table) {
          orderObj.table = table
        } else {
          orderObj.table = {
            _id: orderObj.table,
            number: orderObj.table,
            capacity: 4
          }
        }
      } catch (error) {
        orderObj.table = {
          _id: orderObj.table,
          number: orderObj.table,
          capacity: 4
        }
      }
    } else {
      orderObj.table = {
        _id: orderObj.table,
        number: orderObj.table,
        capacity: 4
      }
    }

    return NextResponse.json({
      success: true,
      data: orderObj
    })
    
  } catch (error: any) {
    console.error('Get order error:', error)
    
    if (error.message === 'Authentication required' || error.message === 'Insufficient permissions') {
      return NextResponse.json(
        { error: error.message },
        { status: 401 }
      )
    }
    
    return NextResponse.json(
      { error: 'Failed to fetch order' },
      { status: 500 }
    )
  }
}

// PUT /api/orders/[id] - Update order
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await requireAuth(request, 'manage_orders')
    await connectDB()

    const { id } = await params
    const data = await request.json()

    // Calculate total amount if items are updated
    if (data.items) {
      let totalAmount = 0
      for (const item of data.items) {
        totalAmount += item.price * item.quantity
      }
      data.totalAmount = totalAmount
      data.finalAmount = Math.max(0, totalAmount - (data.discount || 0))
    }

    const order = await Order.findByIdAndUpdate(
      id,
      data,
      { new: true, runValidators: true }
    )
      .populate('table', 'number capacity')
      .populate('createdBy', 'name')
      .populate('servedBy', 'name')
    
    if (!order) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }
    
    return NextResponse.json({
      success: true,
      message: 'Order updated successfully',
      data: order
    })
    
  } catch (error: any) {
    console.error('Update order error:', error)
    
    if (error.message === 'Authentication required' || error.message === 'Insufficient permissions') {
      return NextResponse.json(
        { error: error.message },
        { status: 401 }
      )
    }
    
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map((err: any) => err.message)
      return NextResponse.json(
        { error: validationErrors.join(', ') },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Failed to update order' },
      { status: 500 }
    )
  }
}

// DELETE /api/orders/[id] - Delete order
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await requireAuth(request, 'manage_orders')
    await connectDB()
    
    const order = await Order.findById(params.id)
    
    if (!order) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }
    
    // Update table status if order is being deleted
    if (order.table) {
      await Table.findByIdAndUpdate(order.table, {
        status: 'available',
        currentOrder: null
      })
    }
    
    await Order.findByIdAndDelete(params.id)
    
    return NextResponse.json({
      success: true,
      message: 'Order deleted successfully'
    })
    
  } catch (error: any) {
    console.error('Delete order error:', error)
    
    if (error.message === 'Authentication required' || error.message === 'Insufficient permissions') {
      return NextResponse.json(
        { error: error.message },
        { status: 401 }
      )
    }
    
    return NextResponse.json(
      { error: 'Failed to delete order' },
      { status: 500 }
    )
  }
}

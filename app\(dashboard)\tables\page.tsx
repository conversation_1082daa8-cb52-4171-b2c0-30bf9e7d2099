"use client"

import { useState, useEffect } from 'react'
import Dashboard<PERSON>eader from '@/components/DashboardHeader'
import {
  Plus,
  Search,
  Edit,
  User,
  Eye,
  X,
  Save,
  Minus,
  ArrowLeft,
  Clock,
  CheckCircle,
  AlertCircle
} from "lucide-react"

interface Table {
  _id: string
  number: number
  capacity: number
  location: string
  status: 'available' | 'occupied' | 'reserved' | 'maintenance'
  isActive: boolean
}

interface Order {
  _id: string
  orderNumber: string
  table: {
    _id: string
    number: number
    capacity: number
  }
  items: Array<{
    menuItem: string
    name: string
    quantity: number
    price: number
    specialInstructions?: string
  }>
  totalAmount: number
  discount: number
  finalAmount: number
  status: string
  paymentStatus: 'pending' | 'paid' | 'refunded'
  customerName?: string
  customerPhone?: string
  specialInstructions?: string
  createdAt: string
  updatedAt: string
}

interface MenuItem {
  _id: string
  name: string
  description: string
  price: number
  basePrice: number
  icon?: string
  mainCategoryId: string | { _id: string; name: string }
  isActive: boolean
}

export default function TablesPage() {
  const [tables, setTables] = useState<Table[]>([])
  const [orders, setOrders] = useState<Order[]>([])
  const [menuItems, setMenuItems] = useState<MenuItem[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')

  // Modal states
  const [showOrderModal, setShowOrderModal] = useState(false)
  const [editingOrder, setEditingOrder] = useState<Order | null>(null)
  const [selectedTable, setSelectedTable] = useState<Table | null>(null)

  // View states
  const [currentView, setCurrentView] = useState<'tables' | 'orders'>('tables')
  const [quickTableNumber, setQuickTableNumber] = useState('')
  const [showQuickApply, setShowQuickApply] = useState(false)

  // Order form data
  const [orderFormData, setOrderFormData] = useState({
    table: '',
    items: [] as Array<{
      menuItem: string
      name: string
      quantity: number
      price: number
      specialInstructions?: string
    }>,
    customerName: '',
    customerPhone: '',
    specialInstructions: '',
    discount: 0
  })

  const filteredTables = Array.isArray(tables) ? tables.filter(table => {
    const matchesSearch = table.number.toString().includes(searchTerm) ||
                         table.location.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || table.status === statusFilter
    return matchesSearch && matchesStatus
  }) : []

  useEffect(() => {
    fetchTables()
    fetchOrders()
    fetchMenuItems()
  }, [])

  const fetchTables = async () => {
    try {
      const response = await fetch('/api/tables')
      if (response.ok) {
        const data = await response.json()
        setTables(Array.isArray(data) ? data : [])
      } else {
        setError('Failed to fetch tables')
        setTables([])
      }
    } catch (error) {
      setError('Error fetching tables')
      setTables([])
    }
  }

  const fetchOrders = async () => {
    try {
      const response = await fetch('/api/orders')
      if (response.ok) {
        const data = await response.json()
        setOrders(Array.isArray(data) ? data : [])
      } else {
        setError('Failed to fetch orders')
        setOrders([])
      }
    } catch (error) {
      setError('Error fetching orders')
      setOrders([])
    } finally {
      setLoading(false)
    }
  }

  const fetchMenuItems = async () => {
    try {
      const response = await fetch('/api/menu-items')
      if (response.ok) {
        const data = await response.json()
        setMenuItems(data)
      }
    } catch (error) {
      console.error('Error fetching menu items:', error)
    }
  }

  const handleCreateOrder = (table: Table) => {
    setSelectedTable(table)
    setEditingOrder(null)
    setOrderFormData({
      table: table._id,
      items: [],
      customerName: '',
      customerPhone: '',
      specialInstructions: '',
      discount: 0
    })
    setShowOrderModal(true)
  }

  const handleEditOrder = (order: Order) => {
    setEditingOrder(order)
    setSelectedTable(null)
    setOrderFormData({
      table: typeof order.table === 'string' ? order.table : order.table._id,
      items: order.items || [],
      customerName: order.customerName || '',
      customerPhone: order.customerPhone || '',
      specialInstructions: order.specialInstructions || '',
      discount: order.discount || 0
    })
    setShowOrderModal(true)
  }

  const getTableOrders = (tableId: string) => {
    return Array.isArray(orders) ? orders.filter(order =>
      typeof order.table === 'object' && order.table._id === tableId
    ) : []
  }

  const handleOrderSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')

    try {
      const url = editingOrder ? `/api/orders/${editingOrder._id}` : '/api/orders'
      const method = editingOrder ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderFormData),
      })

      const result = await response.json()

      if (response.ok) {
        setShowOrderModal(false)
        setEditingOrder(null)
        setSelectedTable(null)
        fetchOrders()
        fetchTables()
      } else {
        setError(result.error || 'Failed to save order')
      }
    } catch (error) {
      setError('An error occurred while saving the order')
    }
  }

  const addOrderItem = () => {
    setOrderFormData(prev => ({
      ...prev,
      items: [...prev.items, { menuItem: '', name: '', quantity: 1, price: 0 }]
    }))
  }

  const updateOrderItem = (index: number, field: string, value: any) => {
    setOrderFormData(prev => ({
      ...prev,
      items: prev.items.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      )
    }))
  }

  const removeOrderItem = (index: number) => {
    setOrderFormData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index)
    }))
  }

  const handleQuickApply = () => {
    if (quickTableNumber.trim()) {
      // Create a virtual table for quick order
      const virtualTable: Table = {
        _id: quickTableNumber,
        number: parseInt(quickTableNumber) || 0,
        capacity: 4,
        location: 'Quick Order',
        status: 'available',
        isActive: true
      }
      handleCreateOrder(virtualTable)
      setQuickTableNumber('')
      setShowQuickApply(false)
    }
  }

  const handleBack = () => {
    if (currentView === 'orders') {
      setCurrentView('tables')
    } else {
      // Navigate back to dashboard
      window.history.back()
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available':
        return 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-300'
      case 'occupied':
        return 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-300'
      case 'reserved':
        return 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-300'
      case 'maintenance':
        return 'bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-300'
      default:
        return 'bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-300'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <DashboardHeader title="Tables" />
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto"></div>
            <p className="mt-4 text-gray-600 dark:text-gray-400">Loading tables...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Mobile-first Header with Back Button */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <button
              onClick={handleBack}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            >
              <ArrowLeft className="w-5 h-5 text-gray-600 dark:text-gray-400" />
            </button>
            <div>
              <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
                {currentView === 'tables' ? 'Tables' : 'Orders'}
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {currentView === 'tables' ? 'Manage restaurant tables' : 'View all orders'}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Quick Apply Table Number */}
            {showQuickApply ? (
              <div className="flex items-center gap-2">
                <input
                  type="text"
                  placeholder="Table number"
                  value={quickTableNumber}
                  onChange={(e) => setQuickTableNumber(e.target.value)}
                  className="w-24 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  onKeyPress={(e) => e.key === 'Enter' && handleQuickApply()}
                />
                <button
                  onClick={handleQuickApply}
                  className="px-3 py-1 bg-orange-600 hover:bg-orange-700 text-white text-sm rounded transition-colors"
                >
                  Apply
                </button>
                <button
                  onClick={() => setShowQuickApply(false)}
                  className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            ) : (
              <>
                <button
                  onClick={() => setShowQuickApply(true)}
                  className="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors"
                >
                  Quick Table
                </button>
                <button
                  onClick={() => setCurrentView(currentView === 'tables' ? 'orders' : 'tables')}
                  className="px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm rounded-lg transition-colors"
                >
                  {currentView === 'tables' ? 'View Orders' : 'View Tables'}
                </button>
              </>
            )}
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        {error && (
          <div className="mb-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <p className="text-red-800 dark:text-red-200">{error}</p>
          </div>
        )}

        {currentView === 'tables' ? (
          <>
            {/* Filters */}
            <div className="mb-6 flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="Search tables..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="all">All Status</option>
                <option value="available">Available</option>
                <option value="occupied">Occupied</option>
                <option value="reserved">Reserved</option>
                <option value="maintenance">Maintenance</option>
              </select>
            </div>

        {/* Tables Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredTables.map((table) => {
            const tableOrders = getTableOrders(table._id)
            const activeOrder = tableOrders.find(order =>
              order.status !== 'completed' && order.status !== 'cancelled'
            )

            return (
              <div
                key={table._id}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
              >
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Table {table.number}
                  </h3>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(table.status)}`}>
                    {table.status}
                  </span>
                </div>

                <div className="space-y-2 mb-4">
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    <User className="w-4 h-4 inline mr-1" />
                    Capacity: {table.capacity} seats
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    📍 {table.location}
                  </p>
                </div>

                {activeOrder && (
                  <div className="mb-4 p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-orange-800 dark:text-orange-200">
                          Order #{activeOrder.orderNumber}
                        </p>
                        <p className="text-xs text-orange-600 dark:text-orange-400">
                          {activeOrder.items.length} items • ₹{activeOrder.finalAmount.toLocaleString('en-IN')}
                        </p>
                      </div>
                      <button className="text-orange-600 hover:text-orange-800 dark:text-orange-400 dark:hover:text-orange-300">
                        <Eye className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                )}

                <div className="flex gap-2">
                  <button
                    onClick={() => handleCreateOrder(table)}
                    className="flex-1 px-3 py-2 bg-orange-600 hover:bg-orange-700 text-white text-sm rounded-lg transition-colors flex items-center justify-center gap-1"
                  >
                    <Plus className="w-4 h-4" />
                    Add Order
                  </button>
                  {activeOrder && (
                    <button
                      onClick={() => handleEditOrder(activeOrder)}
                      className="px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm rounded-lg transition-colors"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </div>
            )
          })}
        </div>

            {filteredTables.length === 0 && (
              <div className="text-center py-12">
                <p className="text-gray-500 dark:text-gray-400">No tables found matching your criteria.</p>
              </div>
            )}
          </>
        ) : (
          /* Orders View */
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">All Orders</h2>

            {orders.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-gray-500 dark:text-gray-400">No orders found.</p>
              </div>
            ) : (
              <div className="grid gap-4">
                {orders.map((order) => (
                  <div key={order._id} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <h3 className="font-semibold text-gray-900 dark:text-white">
                          Order #{order.orderNumber}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          Table: {typeof order.table === 'object' ? `Table ${order.table.number}` : order.table}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-gray-900 dark:text-white">
                          ₹{order.finalAmount.toLocaleString('en-IN')}
                        </p>
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                          order.status === 'completed' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                          order.status === 'preparing' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                          order.status === 'serving' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' :
                          'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
                        }`}>
                          {order.status}
                        </span>
                      </div>
                    </div>

                    {order.items && order.items.length > 0 && (
                      <div className="mb-3">
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Items:</p>
                        <div className="space-y-1">
                          {order.items.slice(0, 3).map((item, index) => (
                            <div key={index} className="flex justify-between text-sm">
                              <span className="text-gray-900 dark:text-white">
                                {item.name} x{item.quantity}
                              </span>
                              <span className="text-gray-600 dark:text-gray-400">
                                ₹{(item.price * item.quantity).toLocaleString('en-IN')}
                              </span>
                            </div>
                          ))}
                          {order.items.length > 3 && (
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              +{order.items.length - 3} more items
                            </p>
                          )}
                        </div>
                      </div>
                    )}

                    {order.customerName && (
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        Customer: {order.customerName}
                      </p>
                    )}

                    <div className="flex gap-2">
                      <button
                        onClick={() => handleEditOrder(order)}
                        className="flex-1 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors"
                      >
                        <Edit className="w-4 h-4 inline mr-1" />
                        Edit
                      </button>
                      <button
                        className="px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm rounded-lg transition-colors"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Order Modal */}
      {showOrderModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-2xl flex flex-col">
            <div className="flex-shrink-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {editingOrder ? 'Edit Order' : 'Create New Order'}
                </h3>
                <button
                  onClick={() => setShowOrderModal(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>
            </div>

            <div className="flex-1 overflow-y-auto overflow-x-hidden">
              <form onSubmit={handleOrderSubmit} className="p-6 space-y-6">
                {error && (
                  <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                    <p className="text-red-800 dark:text-red-200">{error}</p>
                  </div>
                )}

                {/* Table and Customer Info */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Table *
                    </label>
                    <select
                      required
                      value={orderFormData.table}
                      onChange={(e) => setOrderFormData({...orderFormData, table: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="">Select Table</option>
                      {tables.filter(table => table.status === 'available' || table._id === orderFormData.table).map(table => (
                        <option key={table._id} value={table._id}>
                          Table {table.number} ({table.capacity} seats)
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Customer Name (Optional)
                    </label>
                    <input
                      type="text"
                      value={orderFormData.customerName}
                      onChange={(e) => setOrderFormData({...orderFormData, customerName: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      placeholder="Enter customer name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Customer Phone (Optional)
                    </label>
                    <input
                      type="tel"
                      value={orderFormData.customerPhone}
                      onChange={(e) => setOrderFormData({...orderFormData, customerPhone: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      placeholder="Enter phone number"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Discount (₹)
                    </label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={orderFormData.discount}
                      onChange={(e) => setOrderFormData({...orderFormData, discount: parseFloat(e.target.value) || 0})}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      placeholder="0.00"
                    />
                  </div>
                </div>

                {/* Order Items */}
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Order Items
                    </label>
                    <button
                      type="button"
                      onClick={addOrderItem}
                      className="inline-flex items-center px-3 py-1 bg-orange-600 hover:bg-orange-700 text-white text-sm rounded-lg transition-colors"
                    >
                      <Plus className="w-4 h-4 mr-1" />
                      Add Item
                    </button>
                  </div>

                  {orderFormData.items.length === 0 ? (
                    <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                      No items added yet. Click "Add Item" to start building the order.
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {orderFormData.items.map((item, index) => (
                        <div key={index} className="flex gap-3 items-start p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                          <div className="flex-1 grid grid-cols-1 md:grid-cols-4 gap-3">
                            <input
                              type="text"
                              placeholder="Item name"
                              value={item.name}
                              onChange={(e) => updateOrderItem(index, 'name', e.target.value)}
                              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                            />
                            <input
                              type="number"
                              placeholder="Quantity"
                              min="1"
                              value={item.quantity}
                              onChange={(e) => updateOrderItem(index, 'quantity', parseInt(e.target.value) || 1)}
                              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                            />
                            <input
                              type="number"
                              placeholder="Price"
                              min="0"
                              step="0.01"
                              value={item.price}
                              onChange={(e) => updateOrderItem(index, 'price', parseFloat(e.target.value) || 0)}
                              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                            />
                            <input
                              type="text"
                              placeholder="Special instructions"
                              value={item.specialInstructions || ''}
                              onChange={(e) => updateOrderItem(index, 'specialInstructions', e.target.value)}
                              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                            />
                          </div>
                          <button
                            type="button"
                            onClick={() => removeOrderItem(index)}
                            className="p-2 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                          >
                            <Minus className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Special Instructions */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Special Instructions
                  </label>
                  <textarea
                    value={orderFormData.specialInstructions}
                    onChange={(e) => setOrderFormData({...orderFormData, specialInstructions: e.target.value})}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="Any special instructions for this order..."
                  />
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowOrderModal(false)}
                    className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={orderFormData.items.length === 0}
                    className="flex-1 px-4 py-2 bg-orange-600 hover:bg-orange-700 disabled:bg-gray-400 text-white rounded-lg transition-colors flex items-center justify-center gap-2"
                  >
                    <Save className="w-4 h-4" />
                    {editingOrder ? 'Update Order' : 'Create Order'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
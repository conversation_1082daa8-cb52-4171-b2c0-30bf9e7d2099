import { NextRequest, NextResponse } from 'next/server'
import connectDB from '@/lib/mongodb'
import Table from '@/models/Table'
import Order from '@/models/Order'
import MenuItem from '@/models/MenuItem'
import { requireAuth } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    await requireAuth(request, 'view_dashboard')
    await connectDB()
    
    // Get today's date range
    const today = new Date()
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate())
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1)
    
    // Fetch all metrics in parallel for better performance
    const [
      activeTables,
      todayOrders,
      todayEarnings,
      totalMenuItems
    ] = await Promise.all([
      // Active Tables (occupied or reserved)
      Table.countDocuments({ 
        status: { $in: ['occupied', 'reserved'] },
        isActive: true 
      }),
      
      // Today's Orders
      Order.countDocuments({
        createdAt: {
          $gte: startOfDay,
          $lt: endOfDay
        }
      }),
      
      // Today's Earnings (sum of finalAmount for paid orders)
      Order.aggregate([
        {
          $match: {
            createdAt: {
              $gte: startOfDay,
              $lt: endOfDay
            },
            paymentStatus: 'paid'
          }
        },
        {
          $group: {
            _id: null,
            totalEarnings: { $sum: '$finalAmount' }
          }
        }
      ]),
      
      // Total Menu Items (available items only)
      MenuItem.countDocuments({ isAvailable: true })
    ])
    
    // Extract earnings from aggregation result
    const earnings = todayEarnings.length > 0 ? todayEarnings[0].totalEarnings : 0
    
    // Get additional details for better dashboard insights
    const [totalTables, pendingOrders] = await Promise.all([
      Table.countDocuments({ isActive: true }),
      Order.countDocuments({
        status: { $in: ['pending', 'preparing'] },
        createdAt: {
          $gte: startOfDay,
          $lt: endOfDay
        }
      })
    ])
    
    return NextResponse.json({
      success: true,
      data: {
        activeTables: {
          count: activeTables,
          total: totalTables,
          percentage: totalTables > 0 ? Math.round((activeTables / totalTables) * 100) : 0
        },
        todayOrders: {
          count: todayOrders,
          pending: pendingOrders
        },
        todayEarnings: {
          amount: earnings,
          formatted: `₹${earnings.toLocaleString('en-IN')}`
        },
        totalMenuItems: {
          count: totalMenuItems
        }
      },
      timestamp: new Date().toISOString()
    })
    
  } catch (error: any) {
    console.error('Dashboard API error:', error)
    
    if (error.message === 'Authentication required' || error.message === 'Insufficient permissions') {
      return NextResponse.json(
        { error: error.message },
        { status: 401 }
      )
    }
    
    return NextResponse.json(
      { error: 'Failed to fetch dashboard data' },
      { status: 500 }
    )
  }
}

"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Plus, Search, Edit, Trash2, Star, DollarSign } from "lucide-react"

interface MenuItem {
  id: string
  name: string
  description: string
  price: number
  category: string
  image?: string
  isVegetarian: boolean
  isSpicy: boolean
  isPopular: boolean
  variants?: { name: string; price: number }[]
}

export default function MenuPage() {
  const [menuItems] = useState<MenuItem[]>([
    {
      id: "1",
      name: "Butter Chicken",
      description: "Creamy tomato-based curry with tender chicken pieces",
      price: 280,
      category: "Main Course",
      isVegetarian: false,
      isSpicy: true,
      isPopular: true,
      variants: [
        { name: "Regular", price: 280 },
        { name: "Large", price: 350 }
      ]
    },
    {
      id: "2",
      name: "<PERSON>",
      description: "Rich and creamy black lentils cooked with butter and cream",
      price: 180,
      category: "Main Course",
      isVegetarian: true,
      isSpicy: false,
      isPopular: true
    },
    {
      id: "3",
      name: "<PERSON><PERSON>",
      description: "Soft and fluffy Indian bread baked in tandoor",
      price: 40,
      category: "Bread",
      isVegetarian: true,
      isSpicy: false,
      isPopular: false,
      variants: [
        { name: "Plain", price: 40 },
        { name: "Butter", price: 50 },
        { name: "Garlic", price: 60 }
      ]
    },
    {
      id: "4",
      name: "Mango Lassi",
      description: "Refreshing yogurt-based drink with fresh mango",
      price: 80,
      category: "Beverages",
      isVegetarian: true,
      isSpicy: false,
      isPopular: true
    }
  ])

  const [searchTerm, setSearchTerm] = useState("")
  const [categoryFilter, setCategoryFilter] = useState("all")

  const categories = ["all", ...Array.from(new Set(menuItems.map(item => item.category)))]

  const filteredItems = menuItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = categoryFilter === "all" || item.category === categoryFilter
    return matchesSearch && matchesCategory
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Menu Management</h1>
          <p className="text-gray-600 mt-1">Manage your restaurant menu items and categories</p>
        </div>
        <Button className="bg-orange-500 hover:bg-orange-600 text-white">
          <Plus className="w-4 h-4 mr-2" />
          Add Menu Item
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <DollarSign className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Items</p>
              <p className="text-2xl font-bold text-gray-900">{menuItems.length}</p>
            </div>
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <Star className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Popular Items</p>
              <p className="text-2xl font-bold text-gray-900">{menuItems.filter(item => item.isPopular).length}</p>
            </div>
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-orange-100 rounded-lg">
              <span className="text-orange-600 text-xl">🥬</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Vegetarian</p>
              <p className="text-2xl font-bold text-gray-900">{menuItems.filter(item => item.isVegetarian).length}</p>
            </div>
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 rounded-lg">
              <span className="text-red-600 text-xl">🌶️</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Spicy Items</p>
              <p className="text-2xl font-bold text-gray-900">{menuItems.filter(item => item.isSpicy).length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-3 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search menu items..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          <select
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value)}
          >
            {categories.map(category => (
              <option key={category} value={category}>
                {category === "all" ? "All Categories" : category}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Menu Items Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredItems.map((item) => (
          <div key={item.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            {/* Item Image Placeholder */}
            <div className="h-48 bg-gradient-to-br from-orange-100 to-red-100 flex items-center justify-center">
              <span className="text-4xl">🍽️</span>
            </div>

            <div className="p-4">
              {/* Item Header */}
              <div className="flex justify-between items-start mb-2">
                <h3 className="font-semibold text-lg text-gray-900">{item.name}</h3>
                {item.isPopular && (
                  <div className="flex items-center text-yellow-500">
                    <Star className="w-4 h-4 fill-current" />
                  </div>
                )}
              </div>

              {/* Category and Tags */}
              <div className="flex items-center gap-2 mb-2">
                <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full">
                  {item.category}
                </span>
                {item.isVegetarian && (
                  <span className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">
                    🥬 Veg
                  </span>
                )}
                {item.isSpicy && (
                  <span className="px-2 py-1 bg-red-100 text-red-700 text-xs rounded-full">
                    🌶️ Spicy
                  </span>
                )}
              </div>

              {/* Description */}
              <p className="text-sm text-gray-600 mb-3">{item.description}</p>

              {/* Price */}
              <div className="mb-4">
                {item.variants ? (
                  <div className="space-y-1">
                    {item.variants.map((variant, index) => (
                      <div key={index} className="flex justify-between text-sm">
                        <span className="text-gray-700">{variant.name}</span>
                        <span className="font-semibold">₹{variant.price}</span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <span className="text-xl font-bold text-gray-900">₹{item.price}</span>
                )}
              </div>

              {/* Actions */}
              <div className="flex justify-between">
                <Button variant="outline" size="sm">
                  <Edit className="w-4 h-4 mr-2" />
                  Edit
                </Button>
                <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredItems.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Search className="w-12 h-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No menu items found</h3>
          <p className="text-gray-600">Try adjusting your search or filter criteria</p>
        </div>
      )}
    </div>
  )
}

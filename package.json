{"name": "pos<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint", "seed": "tsx scripts/seed.ts"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "dotenv": "^17.2.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.544.0", "mongoose": "^8.18.1", "next": "15.5.3", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-variants": "^3.1.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.3", "tailwindcss": "^4", "tsx": "^4.20.5", "typescript": "^5"}}
import { NextRequest, NextResponse } from 'next/server'
import connectDB from '@/lib/mongodb'
import Table from '@/models/Table'
import { requireAuth } from '@/lib/auth'

// GET /api/tables - Get all tables
export async function GET(request: NextRequest) {
  try {
    await requireAuth(request, 'manage_tables')
    await connectDB()
    
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const location = searchParams.get('location')
    
    let query: any = { isActive: true }
    
    if (status) {
      query.status = status
    }
    
    if (location) {
      query.location = location
    }
    
    const tables = await Table.find(query)
      .populate('currentOrder', 'orderNumber totalAmount status')
      .sort({ number: 1 })
    
    return NextResponse.json({
      success: true,
      data: tables
    })
    
  } catch (error: any) {
    console.error('Get tables error:', error)
    
    if (error.message === 'Authentication required' || error.message === 'Insufficient permissions') {
      return NextResponse.json(
        { error: error.message },
        { status: 401 }
      )
    }
    
    return NextResponse.json(
      { error: 'Failed to fetch tables' },
      { status: 500 }
    )
  }
}

// POST /api/tables - Create new table
export async function POST(request: NextRequest) {
  try {
    await requireAuth(request, 'manage_tables')
    await connectDB()
    
    const data = await request.json()
    
    const table = new Table(data)
    await table.save()
    
    return NextResponse.json({
      success: true,
      message: 'Table created successfully',
      data: table
    }, { status: 201 })
    
  } catch (error: any) {
    console.error('Create table error:', error)
    
    if (error.message === 'Authentication required' || error.message === 'Insufficient permissions') {
      return NextResponse.json(
        { error: error.message },
        { status: 401 }
      )
    }
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }
    
    if (error.code === 11000) {
      return NextResponse.json(
        { error: 'Table number already exists' },
        { status: 409 }
      )
    }
    
    return NextResponse.json(
      { error: 'Failed to create table' },
      { status: 500 }
    )
  }
}

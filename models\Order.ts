import mongoose, { Document, Schema } from 'mongoose'

export interface IOrderItem {
  menuItem: mongoose.Types.ObjectId
  name: string
  variant?: string
  quantity: number
  price: number
  specialInstructions?: string
}

export interface IOrder extends Document {
  orderNumber: string
  table: string // Store as string (can be ObjectId string or custom name)
  items: IOrderItem[]
  status: string
  totalAmount: number
  discount: number
  finalAmount: number
  paymentStatus: 'pending' | 'paid' | 'refunded'
  paymentMethod?: 'cash' | 'card' | 'upi' | 'online'
  customerName?: string
  customerPhone?: string
  specialInstructions?: string
  createdBy: mongoose.Types.ObjectId
  servedBy?: mongoose.Types.ObjectId
  orderTime: Date
  preparedTime?: Date
  servedTime?: Date
  estimatedTime?: Date
  createdAt: Date
  updatedAt: Date
}

const OrderItemSchema = new Schema<IOrderItem>({
  menuItem: {
    type: Schema.Types.ObjectId,
    ref: 'MenuItem',
    required: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  variant: {
    type: String,
    trim: true
  },
  quantity: {
    type: Number,
    required: true,
    min: [1, 'Quantity must be at least 1']
  },
  price: {
    type: Number,
    required: true,
    min: [0, 'Price cannot be negative']
  },
  specialInstructions: {
    type: String,
    trim: true,
    maxlength: [200, 'Special instructions cannot exceed 200 characters']
  }
})

const OrderSchema = new Schema<IOrder>({
  orderNumber: {
    type: String,
    unique: true,
    validate: {
      validator: function(v: string) {
        return v && v.length > 0
      },
      message: 'Order number is required and will be auto-generated'
    }
  },
  table: {
    type: String, // Change to String to handle both ObjectId strings and custom names
    required: true,
    validate: {
      validator: function(v: string) {
        // Allow any non-empty string (ObjectId strings or custom names)
        return typeof v === 'string' && v.trim().length > 0
      },
      message: 'Table must be a non-empty string'
    }
  },
  items: [OrderItemSchema],
  status: {
    type: String,
    default: 'Serving'
  },
  totalAmount: {
    type: Number,
    required: true,
    min: [0, 'Total amount cannot be negative']
  },
  discount: {
    type: Number,
    default: 0,
    min: [0, 'Discount cannot be negative']
  },
  finalAmount: {
    type: Number,
    required: true,
    default: 0
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'paid', 'refunded'],
    default: 'pending'
  },
  paymentMethod: {
    type: String,
    enum: ['cash', 'card', 'upi', 'online']
  },
  customerName: {
    type: String,
    trim: true,
    maxlength: [100, 'Customer name cannot exceed 100 characters']
  },
  customerPhone: {
    type: String,
    trim: true,
    validate: {
      validator: function(v: string) {
        // Allow empty string or exactly 10 digits
        return !v || /^\d{10}$/.test(v)
      },
      message: 'Phone number must be exactly 10 digits'
    }
  },
  specialInstructions: {
    type: String,
    trim: true,
    maxlength: [500, 'Special instructions cannot exceed 500 characters']
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  servedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  orderTime: {
    type: Date,
    default: Date.now
  },
  preparedTime: {
    type: Date
  },
  servedTime: {
    type: Date
  },
  estimatedTime: {
    type: Date
  }
}, {
  timestamps: true
})

// Generate order number before validation
OrderSchema.pre('validate', async function(next) {
  if (this.isNew && !this.orderNumber) {
    try {
      const today = new Date()
      // Format: DDMMYYYY
      const day = String(today.getDate()).padStart(2, '0')
      const month = String(today.getMonth() + 1).padStart(2, '0')
      const year = today.getFullYear()
      const dateStr = `${day}${month}${year}`

      // Get the model constructor
      const OrderModel = this.constructor as any

      // Count orders created today
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate())
      const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1)

      const count = await OrderModel.countDocuments({
        createdAt: {
          $gte: startOfDay,
          $lt: endOfDay
        }
      })

      // Format: 1-20092025, 2-20092025, etc.
      this.orderNumber = `${count + 1}-${dateStr}`
    } catch (error) {
      console.error('Error generating order number:', error)
      // Fallback order number
      const timestamp = Date.now()
      this.orderNumber = `ORD-${timestamp}`
    }
  }
  next()
})

// Additional pre-save hook to ensure order number exists
OrderSchema.pre('save', function(next) {
  if (!this.orderNumber) {
    // Final fallback if somehow orderNumber is still missing
    const timestamp = Date.now()
    this.orderNumber = `ORD-${timestamp}`
  }
  next()
})

// Calculate final amount before saving
OrderSchema.pre('save', function(next) {
  this.finalAmount = this.totalAmount - this.discount
  next()
})

// Indexes for better performance
OrderSchema.index({ orderNumber: 1 }, { unique: true }) // Ensure order numbers are unique
OrderSchema.index({ table: 1, status: 1 }) // Allow duplicate table names
OrderSchema.index({ createdAt: -1 })
OrderSchema.index({ status: 1, orderTime: 1 })

export default mongoose.models?.Order || mongoose.model<IOrder>('Order', OrderSchema)

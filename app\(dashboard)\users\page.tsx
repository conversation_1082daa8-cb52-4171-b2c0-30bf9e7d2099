export default function UsersPage() {
  const users = [
    { id: 1, name: "<PERSON>ks<PERSON> Sompura", email: "<EMAIL>" },
    { id: 2, name: "<PERSON>", email: "<EMAIL>" },
  ]

  return (
    <div>
      <h2 className="text-xl font-bold mb-4">Users</h2>
      <table className="w-full border">
        <thead>
          <tr className="bg-gray-100">
            <th className="p-2 border">ID</th>
            <th className="p-2 border">Name</th>
            <th className="p-2 border">Email</th>
          </tr>
        </thead>
        <tbody>
          {users.map((u) => (
            <tr key={u.id} className="hover:bg-gray-50">
              <td className="p-2 border">{u.id}</td>
              <td className="p-2 border">{u.name}</td>
              <td className="p-2 border">{u.email}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

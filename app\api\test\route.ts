import { NextResponse } from 'next/server'
import connectDB from '@/lib/mongodb'
import User from '@/models/User'
import MenuItem from '@/models/MenuItem'
import Table from '@/models/Table'

export async function GET() {
  try {
    await connectDB()
    
    const userCount = await User.countDocuments()
    const menuItemCount = await MenuItem.countDocuments()
    const tableCount = await Table.countDocuments()
    
    return NextResponse.json({
      success: true,
      message: 'Database connection successful!',
      data: {
        users: userCount,
        menuItems: menuItemCount,
        tables: tableCount
      }
    })
    
  } catch (error) {
    console.error('Database test error:', error)
    return NextResponse.json(
      { error: 'Database connection failed', details: error },
      { status: 500 }
    )
  }
}

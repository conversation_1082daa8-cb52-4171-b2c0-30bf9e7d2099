@import "tailwindcss";

/* Light mode variables */
:root {
  --background: #f3f4f6;
  --foreground: #111827;
}

/* Dark mode variables - class-based */
.dark {
  --background: #111827;
  --foreground: #f9fafb;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Force dark mode styles */
.dark body {
  background-color: #111827 !important;
  color: #f9fafb !important;
}

.dark .bg-white {
  background-color: #374151 !important;
}

.dark .bg-gray-100 {
  background-color: #111827 !important;
}

.dark .text-gray-900 {
  color: #f9fafb !important;
}

.dark .text-gray-600 {
  color: #d1d5db !important;
}

.dark .text-gray-400 {
  color: #9ca3af !important;
}

/* Visual indicator for dark mode */
.dark::before {
  position: fixed;
  top: 10px;
  right: 10px;
  background: red;
  color: white;
  padding: 5px;
  z-index: 9999;
  font-size: 12px;
}

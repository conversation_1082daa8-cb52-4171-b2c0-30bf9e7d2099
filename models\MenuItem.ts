import mongoose, { Document, Schema } from 'mongoose'

export interface IVariant {
  name: string
  price: number
  isAvailable: boolean
}

export interface IMenuItem extends Document {
  name: string
  description: string
  category: string
  subCategory?: string // New field for sub-categories
  basePrice: number
  variants?: IVariant[]
  isAvailable: boolean
  isVegetarian: boolean
  isSpicy: boolean
  preparationTime: number // in minutes
  ingredients: string[]
  allergens: string[]
  image?: string
  icon?: string // Food icon URL or identifier
  daySpecific?: string // For day-wise specials (Monday, Tuesday, etc.)
  createdAt: Date
  updatedAt: Date
}

const VariantSchema = new Schema<IVariant>({
  name: {
    type: String,
    required: true,
    trim: true
  },
  price: {
    type: Number,
    required: true,
    min: [0, 'Price cannot be negative']
  },
  isAvailable: {
    type: Boolean,
    default: true
  }
})

const MenuItemSchema = new Schema<IMenuItem>({
  name: {
    type: String,
    required: [true, 'Item name is required'],
    trim: true,
    maxlength: [100, 'Item name cannot be more than 100 characters']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot be more than 500 characters']
  },
  category: {
    type: String,
    required: [true, 'Category is required'],
    enum: [
      'Kathiyawadi Bhojan',
      'Sanj Ni Special Vangi',
      'Fixed Sanj'
    ]
  },
  subCategory: {
    type: String,
    trim: true
  },
  basePrice: {
    type: Number,
    required: [true, 'Base price is required'],
    min: [0, 'Price cannot be negative']
  },
  variants: [VariantSchema],
  isAvailable: {
    type: Boolean,
    default: true
  },
  isVegetarian: {
    type: Boolean,
    default: true
  },
  isSpicy: {
    type: Boolean,
    default: false
  },
  preparationTime: {
    type: Number,
    default: 15,
    min: [1, 'Preparation time must be at least 1 minute']
  },
  ingredients: [{
    type: String,
    trim: true
  }],
  allergens: [{
    type: String,
    enum: ['Dairy', 'Gluten', 'Nuts', 'Soy', 'Eggs', 'Shellfish']
  }],
  image: {
    type: String,
    trim: true
  },
  icon: {
    type: String,
    trim: true
  },
  daySpecific: {
    type: String,
    enum: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],
    trim: true
  }
}, {
  timestamps: true
})

// Index for better search performance
MenuItemSchema.index({ name: 'text', description: 'text' })
MenuItemSchema.index({ category: 1, isAvailable: 1 })

export default mongoose.models?.MenuItem || mongoose.model<IMenuItem>('MenuItem', MenuItemSchema)

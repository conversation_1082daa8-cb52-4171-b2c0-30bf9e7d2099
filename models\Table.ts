import mongoose, { Document, Schema } from 'mongoose'

export interface ITable extends Document {
  number: number
  capacity: number
  status: 'available' | 'occupied' | 'reserved' | 'cleaning'
  currentOrder?: mongoose.Types.ObjectId
  location: string // 'indoor', 'outdoor', 'private'
  isActive: boolean
  reservedBy?: string
  reservedAt?: Date
  occupiedAt?: Date
  createdAt: Date
  updatedAt: Date
}

const TableSchema = new Schema<ITable>({
  number: {
    type: Number,
    required: [true, 'Table number is required'],
    unique: true,
    min: [1, 'Table number must be positive']
  },
  capacity: {
    type: Number,
    required: [true, 'Table capacity is required'],
    min: [1, 'Capacity must be at least 1'],
    max: [20, 'Capacity cannot exceed 20']
  },
  status: {
    type: String,
    enum: ['available', 'occupied', 'reserved', 'cleaning'],
    default: 'available'
  },
  currentOrder: {
    type: Schema.Types.ObjectId,
    ref: 'Order',
    default: null
  },
  location: {
    type: String,
    enum: ['indoor', 'outdoor', 'private'],
    default: 'indoor'
  },
  isActive: {
    type: Boolean,
    default: true
  },
  reservedBy: {
    type: String,
    trim: true
  },
  reservedAt: {
    type: Date
  },
  occupiedAt: {
    type: Date
  }
}, {
  timestamps: true
})

// Middleware to clear reservation fields when status changes
TableSchema.pre('save', function(next) {
  if (this.isModified('status')) {
    if (this.status !== 'reserved') {
      this.reservedBy = undefined
      this.reservedAt = undefined
    }
    if (this.status !== 'occupied') {
      this.occupiedAt = undefined
    }
    if (this.status === 'occupied' && !this.occupiedAt) {
      this.occupiedAt = new Date()
    }
  }
  next()
})

// Index for better query performance
TableSchema.index({ status: 1, isActive: 1 })
TableSchema.index({ number: 1 })

export default mongoose.models?.Table || mongoose.model<ITable>('Table', TableSchema)
